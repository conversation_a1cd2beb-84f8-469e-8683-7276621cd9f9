# backend/retry_utils.py
"""
统一的重试机制工具模块
提供装饰器和函数级别的重试功能
"""
import time
import logging
import functools
from typing import Callable, Any, Optional, Tuple, Type, Union, List
from enum import Enum

logger = logging.getLogger(__name__)

class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED = "fixed"  # 固定间隔
    EXPONENTIAL = "exponential"  # 指数退避
    LINEAR = "linear"  # 线性增长

class RetryConfig:
    """重试配置类"""
    def __init__(self,
                 max_attempts: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
                 backoff_factor: float = 2.0,
                 jitter: bool = True,
                 exceptions: Tuple[Type[Exception], ...] = (Exception,)):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.strategy = strategy
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.exceptions = exceptions

def calculate_delay(attempt: int, config: RetryConfig) -> float:
    """计算重试延迟时间"""
    if config.strategy == RetryStrategy.FIXED:
        delay = config.base_delay
    elif config.strategy == RetryStrategy.LINEAR:
        delay = config.base_delay * attempt
    else:  # EXPONENTIAL
        delay = config.base_delay * (config.backoff_factor ** (attempt - 1))
    
    # 限制最大延迟
    delay = min(delay, config.max_delay)
    
    # 添加抖动避免雷群效应
    if config.jitter:
        import random
        delay = delay * (0.5 + random.random() * 0.5)
    
    return delay

def retry_on_exception(config: RetryConfig = None):
    """重试装饰器"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    return func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts:
                        logger.error(f"{func.__name__} 重试失败，已达到最大尝试次数 {config.max_attempts}")
                        raise e
                    
                    delay = calculate_delay(attempt, config)
                    logger.warning(f"{func.__name__} 第{attempt}次尝试失败: {e}, {delay:.1f}秒后重试")
                    time.sleep(delay)
            
            # 理论上不会到达这里
            raise last_exception
        
        return wrapper
    return decorator

def retry_async_on_exception(config: RetryConfig = None):
    """异步重试装饰器"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            import asyncio
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts:
                        logger.error(f"{func.__name__} 异步重试失败，已达到最大尝试次数 {config.max_attempts}")
                        raise e
                    
                    delay = calculate_delay(attempt, config)
                    logger.warning(f"{func.__name__} 第{attempt}次异步尝试失败: {e}, {delay:.1f}秒后重试")
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator

def execute_with_retry(func: Callable, 
                      args: tuple = (), 
                      kwargs: dict = None, 
                      config: RetryConfig = None) -> Any:
    """执行函数并重试"""
    if kwargs is None:
        kwargs = {}
    if config is None:
        config = RetryConfig()
    
    last_exception = None
    
    for attempt in range(1, config.max_attempts + 1):
        try:
            return func(*args, **kwargs)
        except config.exceptions as e:
            last_exception = e
            
            if attempt == config.max_attempts:
                logger.error(f"函数执行重试失败，已达到最大尝试次数 {config.max_attempts}")
                raise e
            
            delay = calculate_delay(attempt, config)
            logger.warning(f"函数执行第{attempt}次尝试失败: {e}, {delay:.1f}秒后重试")
            time.sleep(delay)
    
    raise last_exception

# 预定义的重试配置
class RetryConfigs:
    """预定义的重试配置"""
    
    # 数据库操作重试
    DATABASE = RetryConfig(
        max_attempts=3,
        base_delay=0.5,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=(Exception,)
    )
    
    # HTTP请求重试
    HTTP_REQUEST = RetryConfig(
        max_attempts=3,
        base_delay=1.0,
        max_delay=10.0,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=(ConnectionError, TimeoutError, OSError)
    )
    
    # 文件操作重试
    FILE_OPERATION = RetryConfig(
        max_attempts=2,
        base_delay=0.1,
        strategy=RetryStrategy.FIXED,
        exceptions=(OSError, IOError, PermissionError)
    )
    
    # 图片处理重试
    IMAGE_PROCESSING = RetryConfig(
        max_attempts=2,
        base_delay=0.5,
        strategy=RetryStrategy.LINEAR,
        exceptions=(Exception,)
    )
    
    # 快速重试（用于轻量级操作）
    QUICK = RetryConfig(
        max_attempts=2,
        base_delay=0.1,
        strategy=RetryStrategy.FIXED,
        exceptions=(Exception,)
    )

# 便捷的重试装饰器
def retry_database(func: Callable) -> Callable:
    """数据库操作重试装饰器"""
    return retry_on_exception(RetryConfigs.DATABASE)(func)

def retry_http(func: Callable) -> Callable:
    """HTTP请求重试装饰器"""
    return retry_on_exception(RetryConfigs.HTTP_REQUEST)(func)

def retry_file_op(func: Callable) -> Callable:
    """文件操作重试装饰器"""
    return retry_on_exception(RetryConfigs.FILE_OPERATION)(func)

def retry_image_processing(func: Callable) -> Callable:
    """图片处理重试装饰器"""
    return retry_on_exception(RetryConfigs.IMAGE_PROCESSING)(func)

def retry_quick(func: Callable) -> Callable:
    """快速重试装饰器"""
    return retry_on_exception(RetryConfigs.QUICK)(func)

# 上下文管理器版本
class RetryContext:
    """重试上下文管理器"""
    
    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self.attempt = 0
        self.last_exception = None
    
    def __enter__(self):
        self.attempt += 1
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type and issubclass(exc_type, self.config.exceptions):
            self.last_exception = exc_val
            
            if self.attempt < self.config.max_attempts:
                delay = calculate_delay(self.attempt, self.config)
                logger.warning(f"重试上下文第{self.attempt}次尝试失败: {exc_val}, {delay:.1f}秒后重试")
                time.sleep(delay)
                return True  # 抑制异常，继续重试
            else:
                logger.error(f"重试上下文失败，已达到最大尝试次数 {self.config.max_attempts}")
                return False  # 不抑制异常
        
        return False

def with_retry(config: RetryConfig = None):
    """重试上下文管理器工厂函数"""
    return RetryContext(config)

# 示例用法函数
def example_usage():
    """重试机制使用示例"""
    
    # 1. 装饰器用法
    @retry_database
    def database_operation():
        # 数据库操作代码
        pass
    
    @retry_http
    def http_request():
        # HTTP请求代码
        pass
    
    # 2. 函数调用用法
    def some_operation():
        # 一些可能失败的操作
        pass
    
    result = execute_with_retry(
        some_operation,
        config=RetryConfigs.QUICK
    )
    
    # 3. 上下文管理器用法
    for _ in range(RetryConfigs.DATABASE.max_attempts):
        with with_retry(RetryConfigs.DATABASE) as retry_ctx:
            # 数据库操作代码
            pass
        
        if not retry_ctx.last_exception:
            break  # 成功，退出重试循环
