# backend/cache_strategies.py
"""
智能缓存清理策略模块
提供多种缓存清理策略和自动管理功能
"""
import os
import time
import logging
import threading
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CacheStrategy(Enum):
    """缓存清理策略枚举"""
    LRU = "lru"  # 最近最少使用
    SIZE_BASED = "size_based"  # 基于大小
    TIME_BASED = "time_based"  # 基于时间
    HYBRID = "hybrid"  # 混合策略

@dataclass
class CacheStats:
    """缓存统计信息"""
    total_files: int = 0
    total_size_mb: float = 0.0
    oldest_file_age_hours: float = 0.0
    newest_file_age_hours: float = 0.0
    avg_file_size_mb: float = 0.0
    last_cleanup_time: Optional[datetime] = None
    cleanup_count: int = 0

@dataclass
class CacheConfig:
    """缓存配置"""
    max_size_mb: float = 100.0  # 最大缓存大小(MB)
    max_files: int = 50  # 最大文件数量
    max_age_hours: int = 168  # 最大保存时间(小时，默认7天)
    cleanup_interval_minutes: int = 60  # 清理间隔(分钟)
    strategy: CacheStrategy = CacheStrategy.HYBRID
    
class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str, config: CacheConfig = None):
        self.cache_dir = cache_dir
        self.config = config or CacheConfig()
        self.stats = CacheStats()
        self.lock = threading.Lock()
        self._access_times = {}  # 文件访问时间记录
        self._cleanup_thread = None
        self._running = False
        
    def start_auto_cleanup(self):
        """启动自动清理"""
        if self._running:
            return
            
        self._running = True
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()
        logger.info(f"启动智能缓存自动清理 - 间隔: {self.config.cleanup_interval_minutes}分钟")
    
    def stop_auto_cleanup(self):
        """停止自动清理"""
        self._running = False
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
    
    def _cleanup_loop(self):
        """清理循环"""
        while self._running:
            try:
                self.cleanup_if_needed()
                time.sleep(self.config.cleanup_interval_minutes * 60)
            except Exception as e:
                logger.error(f"自动缓存清理失败: {e}")
                time.sleep(300)  # 出错后等待5分钟
    
    def record_access(self, filename: str):
        """记录文件访问"""
        with self.lock:
            self._access_times[filename] = time.time()
    
    def get_cache_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self.lock:
            return self._calculate_stats()
    
    def _calculate_stats(self) -> CacheStats:
        """计算缓存统计信息"""
        if not os.path.exists(self.cache_dir):
            return CacheStats()
        
        files = [f for f in os.listdir(self.cache_dir) if f.endswith('.jpg')]
        if not files:
            return CacheStats()
        
        total_size = 0
        file_ages = []
        current_time = time.time()
        
        for filename in files:
            filepath = os.path.join(self.cache_dir, filename)
            try:
                stat = os.stat(filepath)
                total_size += stat.st_size
                age_hours = (current_time - stat.st_mtime) / 3600
                file_ages.append(age_hours)
            except OSError:
                continue
        
        stats = CacheStats(
            total_files=len(files),
            total_size_mb=total_size / (1024 * 1024),
            oldest_file_age_hours=max(file_ages) if file_ages else 0,
            newest_file_age_hours=min(file_ages) if file_ages else 0,
            avg_file_size_mb=(total_size / len(files)) / (1024 * 1024) if files else 0,
            last_cleanup_time=self.stats.last_cleanup_time,
            cleanup_count=self.stats.cleanup_count
        )
        
        return stats
    
    def cleanup_if_needed(self) -> Dict[str, int]:
        """根据策略清理缓存"""
        with self.lock:
            stats = self._calculate_stats()
            
            # 检查是否需要清理
            needs_cleanup = (
                stats.total_size_mb > self.config.max_size_mb or
                stats.total_files > self.config.max_files or
                stats.oldest_file_age_hours > self.config.max_age_hours
            )
            
            if not needs_cleanup:
                return {"deleted": 0, "reason": "no_cleanup_needed"}
            
            # 执行清理
            deleted_count = self._execute_cleanup(stats)
            
            # 更新统计
            self.stats.last_cleanup_time = datetime.now()
            self.stats.cleanup_count += 1
            
            logger.info(f"缓存清理完成 - 删除文件: {deleted_count}, 策略: {self.config.strategy.value}")
            
            return {
                "deleted": deleted_count,
                "reason": "cleanup_executed",
                "strategy": self.config.strategy.value
            }
    
    def _execute_cleanup(self, stats: CacheStats) -> int:
        """执行清理操作"""
        if self.config.strategy == CacheStrategy.HYBRID:
            return self._hybrid_cleanup()
        elif self.config.strategy == CacheStrategy.LRU:
            return self._lru_cleanup()
        elif self.config.strategy == CacheStrategy.SIZE_BASED:
            return self._size_based_cleanup()
        elif self.config.strategy == CacheStrategy.TIME_BASED:
            return self._time_based_cleanup()
        else:
            return self._hybrid_cleanup()
    
    def _hybrid_cleanup(self) -> int:
        """混合策略清理"""
        deleted_count = 0
        
        # 1. 首先删除过期文件
        deleted_count += self._time_based_cleanup()
        
        # 2. 如果还需要清理，按LRU策略删除
        stats = self._calculate_stats()
        if stats.total_size_mb > self.config.max_size_mb or stats.total_files > self.config.max_files:
            deleted_count += self._lru_cleanup()
        
        return deleted_count
    
    def _lru_cleanup(self) -> int:
        """LRU策略清理"""
        files_info = self._get_files_with_access_time()
        if not files_info:
            return 0
        
        # 按访问时间排序，最久未访问的在前
        files_info.sort(key=lambda x: x[1])
        
        deleted_count = 0
        target_files = max(0, len(files_info) - self.config.max_files + 10)  # 多删除10个，避免频繁清理
        
        for filepath, _ in files_info[:target_files]:
            try:
                os.remove(filepath)
                filename = os.path.basename(filepath)
                self._access_times.pop(filename, None)
                deleted_count += 1
            except OSError as e:
                logger.error(f"删除缓存文件失败: {filepath}, 错误: {e}")
        
        return deleted_count
    
    def _size_based_cleanup(self) -> int:
        """基于大小的清理"""
        files_info = self._get_files_with_size()
        if not files_info:
            return 0
        
        # 按文件大小排序，大文件在前
        files_info.sort(key=lambda x: x[1], reverse=True)
        
        deleted_count = 0
        current_size = sum(size for _, size in files_info) / (1024 * 1024)
        target_size = self.config.max_size_mb * 0.8  # 清理到80%
        
        for filepath, size in files_info:
            if current_size <= target_size:
                break
            
            try:
                os.remove(filepath)
                current_size -= size / (1024 * 1024)
                deleted_count += 1
            except OSError as e:
                logger.error(f"删除缓存文件失败: {filepath}, 错误: {e}")
        
        return deleted_count
    
    def _time_based_cleanup(self) -> int:
        """基于时间的清理"""
        if not os.path.exists(self.cache_dir):
            return 0
        
        deleted_count = 0
        current_time = time.time()
        max_age_seconds = self.config.max_age_hours * 3600
        
        for filename in os.listdir(self.cache_dir):
            if not filename.endswith('.jpg'):
                continue
            
            filepath = os.path.join(self.cache_dir, filename)
            try:
                stat = os.stat(filepath)
                if current_time - stat.st_mtime > max_age_seconds:
                    os.remove(filepath)
                    self._access_times.pop(filename, None)
                    deleted_count += 1
            except OSError as e:
                logger.error(f"删除过期缓存文件失败: {filepath}, 错误: {e}")
        
        return deleted_count
    
    def _get_files_with_access_time(self) -> List[Tuple[str, float]]:
        """获取文件及其访问时间"""
        if not os.path.exists(self.cache_dir):
            return []
        
        files_info = []
        current_time = time.time()
        
        for filename in os.listdir(self.cache_dir):
            if not filename.endswith('.jpg'):
                continue
            
            filepath = os.path.join(self.cache_dir, filename)
            # 使用记录的访问时间，如果没有则使用文件修改时间
            access_time = self._access_times.get(filename)
            if access_time is None:
                try:
                    access_time = os.path.getmtime(filepath)
                except OSError:
                    access_time = current_time
            
            files_info.append((filepath, access_time))
        
        return files_info
    
    def _get_files_with_size(self) -> List[Tuple[str, int]]:
        """获取文件及其大小"""
        if not os.path.exists(self.cache_dir):
            return []
        
        files_info = []
        
        for filename in os.listdir(self.cache_dir):
            if not filename.endswith('.jpg'):
                continue
            
            filepath = os.path.join(self.cache_dir, filename)
            try:
                size = os.path.getsize(filepath)
                files_info.append((filepath, size))
            except OSError:
                continue
        
        return files_info

# 全局智能缓存管理器实例
smart_cache_manager = None

def get_smart_cache_manager(cache_dir: str = None, config: CacheConfig = None) -> SmartCacheManager:
    """获取智能缓存管理器实例"""
    global smart_cache_manager
    
    if smart_cache_manager is None and cache_dir:
        smart_cache_manager = SmartCacheManager(cache_dir, config)
        smart_cache_manager.start_auto_cleanup()
    
    return smart_cache_manager
